import { Prisma } from "@prisma/client";
import { FlinkkInventoryAPI } from "@flinkk/inventory-api";

/**
 * Extends Prisma Client with extension for quote-to-inventory cart sync
 * This extension automatically creates a cart in the inventory service when a Quote is created:
 * 1. Checks if inventory configuration exists for the tenant
 * 2. Retrieves contact email from the contact associated with the quote
 * 3. Creates a cart in the inventory service with email, region_id, and currency_code
 * 4. Optionally includes customer_id if available in the contact record
 * 5. Updates the quote with the inventory_cart_id
 *
 * @returns The Prisma Client extension
 */

// Default values for cart creation
const DEFAULT_REGION_ID = "reg_01JP9R0NP6B5DXGDYHFSSW0FK1";

export function extendQuoteCartSyncMiddleware() {
  return Prisma.defineExtension({
    name: "quoteCartSync",
    query: {
      quote: {
        async create({ args, query }) {
          // First, execute the quote creation
          const result = await query(args);

          // Then, try to create a cart in inventory
          try {
            const quoteId = result.id;

            // Extract tenantId and contactId from the result or args
            // The tenantId and contactId might be in relationships or direct fields
            let tenantId = result.tenantId || args.data.tenantId;
            let contactId = result.contactId || args.data.contactId;

            // If tenantId is in a relationship, extract it
            if (!tenantId && args.data.tenant?.connect?.id) {
              tenantId = args.data.tenant.connect.id;
            }

            // If contactId is in a relationship, extract it
            if (!contactId && args.data.contact?.connect?.id) {
              contactId = args.data.contact.connect.id;
            }

            console.log(
              `🔍 Debug: tenantId=${tenantId} (type: ${typeof tenantId}), contactId=${contactId} (type: ${typeof contactId})`
            );
            console.log(
              `🔍 Debug: tenantId truthy: ${!!tenantId}, contactId truthy: ${!!contactId}`
            );

            if (contactId && tenantId) {
              console.log(
                `🔄 Post-sync: Attempting cart creation for quote: ${quoteId}`
              );

              // Get inventory configuration for the tenant
              const { PrismaClient } = await import("@prisma/client");
              const tempPrisma = new PrismaClient();

              const inventoryConfig =
                await tempPrisma.inventoryConfiguration.findUnique({
                  where: { tenantId },
                  select: {
                    apiUrl: true,
                    token: true,
                    isActive: true,
                  },
                });

              if (inventoryConfig && inventoryConfig.isActive) {
                console.log(
                  `✅ Found active inventory configuration for tenant ${tenantId}`
                );

                // Get contact information
                const contact = await tempPrisma.contact.findUnique({
                  where: { id: contactId },
                  select: {
                    email: true,
                    inventoryCustomerId: true,
                  },
                });

                if (contact && contact.email) {
                  console.log(`✅ Found contact with email: ${contact.email}`);

                  // Create inventory API instance
                  const inventoryAPI = new FlinkkInventoryAPI({
                    apiUrl: inventoryConfig.apiUrl,
                    token: inventoryConfig.token,
                  });

                  // Determine currency code from quote data
                  const currencyCode = "GBP";

                  // Create cart in inventory - use customer_id if available, otherwise just email
                  const cartData = {
                    region_id: DEFAULT_REGION_ID,
                    email: contact.email,
                    currency_code: currencyCode,
                    ...(contact.inventoryCustomerId && {}),
                  };

                  console.log(`🛒 Creating cart with data:`, cartData);

                  const cartResponse = await inventoryAPI.createCart(cartData);

                  if (cartResponse.cart && cartResponse.cart.id) {
                    const cartId = cartResponse.cart.id;
                    const customerId = cartResponse.cart.customer_id;
                    console.log(`✅ Cart created successfully: ${cartId}`);

                    // Update the quote with the cart ID
                    await tempPrisma.quote.update({
                      where: { id: quoteId },
                      data: { inventoryCartId: cartId },
                    });

                    console.log(
                      `✅ Quote updated with inventory cart ID: ${cartId}`
                    );

                    // Create primary traveller profile if we have customer_id
                    if (customerId) {
                      try {
                        // Get contact details for traveller profile
                        const contactDetails =
                          await tempPrisma.contact.findUnique({
                            where: { id: contactId },
                            select: {
                              firstName: true,
                              lastName: true,
                              email: true,
                            },
                          });

                        if (
                          contactDetails &&
                          contactDetails.firstName &&
                          contactDetails.lastName
                        ) {
                          const travellerData: any = {
                            first_name: contactDetails.firstName,
                            last_name: contactDetails.lastName,
                            date_of_birth: "2015-08-23",
                            is_primary: true,
                            gender: "male",
                            relationship: "other",
                            customer_id: customerId,
                          };

                          console.log(
                            `🧳 Creating primary traveller profile:`,
                            travellerData
                          );

                          const travellerResponse =
                            await inventoryAPI.createTravellerProfile(
                              cartId,
                              travellerData
                            );

                          if (travellerResponse.traveller_profile) {
                            console.log(
                              `✅ Primary traveller profile created: ${travellerResponse.traveller_profile.id}`
                            );
                          } else {
                            console.error(
                              `❌ Primary traveller profile creation failed: Invalid response`,
                              travellerResponse
                            );
                          }
                        } else {
                          console.log(
                            `❌ Cannot create primary traveller profile: Contact missing first/last name`
                          );
                        }
                      } catch (travellerError: any) {
                        console.error(
                          "❌ Error creating primary traveller profile:",
                          travellerError
                        );

                        // Check if this is a 404 error (endpoint doesn't exist)
                        if (
                          travellerError.message &&
                          travellerError.message.includes("404")
                        ) {
                          console.log(
                            "ℹ️ Traveller profiles API not available yet - this is expected for new installations"
                          );
                        } else {
                          console.log(
                            "ℹ️ Traveller profile creation failed but quote creation will continue"
                          );
                        }
                        // Continue without failing the quote creation
                      }
                    } else {
                      console.log(
                        `❌ Cannot create primary traveller profile: No customer_id in cart response`
                      );
                    }

                    // Update the result object to include the cart ID
                    result.inventoryCartId = cartId;
                  } else {
                    console.error(
                      `❌ Cart creation failed: Invalid response`,
                      cartResponse
                    );
                  }
                } else {
                  console.log(
                    `❌ Cannot create cart: Contact not found or missing email`
                  );
                }
              } else {
                console.log(
                  `❌ Cart creation skipped: No active inventory configuration for tenant ${tenantId}`
                );
              }

              await tempPrisma.$disconnect();
            } else {
              console.log(
                `❌ Cart creation skipped: contactId=${contactId} (truthy: ${!!contactId}), tenantId=${tenantId} (truthy: ${!!tenantId})`
              );
            }
          } catch (error) {
            console.error("Error in post-sync cart creation:", error);
            // Continue without failing the quote creation
          }

          return result;
        },
      },
    },
  });
}
