"use client";

import React, { useState, useEffect } from "react";
import { Control, useController } from "react-hook-form";
import { Card, CardContent } from "@flinkk/components/ui/card";
import { RadioGroup, RadioGroupItem } from "@flinkk/components/ui/radio-group";
import { Label } from "@flinkk/components/ui/label";
import { Loader2Icon } from "lucide-react";
import { MealPlan } from "@flinkk/inventory-api";

interface MealPlanSelectionProps {
  control: Control<any>;
  hotelId?: string;
  onMealPlanChange?: (mealPlanId: string) => void;
  disabled?: boolean;
}

interface MealPlanResponse {
  meal_plans: MealPlan[];
}

export function MealPlanSelection({
  control,
  hotelId,
  onMealPlanChange,
  disabled = false,
}: MealPlanSelectionProps) {
  const [mealPlans, setMealPlans] = useState<MealPlan[]>([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const {
    field: { value, onChange },
  } = useController({
    name: "selectedMealPlan",
    control,
    defaultValue: "",
  });

  // Fetch meal plans when hotel changes
  useEffect(() => {
    if (!hotelId) {
      setMealPlans([]);
      return;
    }

    const fetchMealPlans = async () => {
      setLoading(true);
      setError(null);

      try {
        const response = await fetch(
          `/api/hotel-management/meal-plan?hotel_id=${hotelId}`
        );

        if (!response.ok) {
          throw new Error(`Failed to fetch meal plans: ${response.statusText}`);
        }

        const data: MealPlanResponse = await response.json();
        setMealPlans(data.meal_plans || []);

        // Auto-select default meal plan if available and no current selection
        if (!value && data.meal_plans?.length > 0) {
          const defaultMealPlan = data.meal_plans.find(
            (plan) => plan.is_default
          );
          if (defaultMealPlan) {
            onChange(defaultMealPlan.id);
            onMealPlanChange?.(defaultMealPlan.id);
          }
        }
      } catch (err) {
        console.error("Error fetching meal plans:", err);
        setError(
          err instanceof Error ? err.message : "Failed to fetch meal plans"
        );
      } finally {
        setLoading(false);
      }
    };

    fetchMealPlans();
  }, [hotelId, value, onChange, onMealPlanChange]);

  const handleMealPlanChange = (mealPlanId: string) => {
    onChange(mealPlanId);
    onMealPlanChange?.(mealPlanId);
  };

  // Don't render if no meal plans available
  if (!loading && !error && mealPlans.length === 0) {
    return null;
  }

  return (
    <div>
      {loading && (
        <div className="flex items-center justify-center py-4">
          <Loader2Icon className="h-4 w-4 animate-spin mr-2" />
          <span className="text-sm text-muted-foreground">
            Loading meal plans...
          </span>
        </div>
      )}

      {error && (
        <div className="text-center py-4">
          <p className="text-sm text-red-600">{error}</p>
        </div>
      )}

      {!loading && !error && mealPlans.length > 0 && (
        <RadioGroup
          value={value}
          onValueChange={handleMealPlanChange}
          disabled={disabled}
          className="space-y-2 grid grid-cols-2"
        >
          {mealPlans.map((mealPlan) => (
            <div
              key={mealPlan.id}
              className="flex items-center space-x-3 p-3 border rounded-lg hover:bg-gray-50 transition-colors"
            >
              <RadioGroupItem
                value={mealPlan.id}
                id={`meal-plan-${mealPlan.id}`}
                disabled={disabled}
              />
              <Label
                htmlFor={`meal-plan-${mealPlan.id}`}
                className="flex-1 cursor-pointer"
              >
                <div className="flex items-center justify-between">
                  <div>
                    <div className="font-medium text-sm">
                      {mealPlan.title || mealPlan.name}
                    </div>
                    {mealPlan.description && (
                      <div className="text-xs text-muted-foreground mt-1">
                        {mealPlan.description}
                      </div>
                    )}
                  </div>
                  {mealPlan.is_default && (
                    <span className="text-xs bg-blue-100 text-blue-800 px-2 py-1 rounded-full">
                      Default
                    </span>
                  )}
                </div>
              </Label>
            </div>
          ))}
        </RadioGroup>
      )}
    </div>
  );
}
