"use client";

import React, { useState, useEffect } from "react";
import { Control, useFieldArray, useWatch } from "react-hook-form";
import {
  Card,
  CardContent,
  CardHeader,
  CardTitle,
} from "@flinkk/components/ui/card";
import { But<PERSON> } from "@flinkk/components/ui/button";
import { Input } from "@flinkk/components/ui/input";
import { Label } from "@flinkk/components/ui/label";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@flinkk/components/ui/select";
import { Badge } from "@flinkk/components/ui/badge";
import { Trash2, Plus, User, Users, Info, HelpCircle } from "lucide-react";
import { toast } from "sonner";
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from "@flinkk/components/ui/tooltip";

// Age classification constants
const AGE_CLASSIFICATIONS = {
  INFANT: { min: 0, max: 2, label: "Infant" },
  CHILD: { min: 3, max: 12, label: "Child" },
  ADULT: { min: 13, max: 999, label: "Adult" },
} as const;

// Country list (you can expand this or fetch from an API)
const COUNTRIES = [
  "United States",
  "United Kingdom",
  "Canada",
  "Australia",
  "Germany",
  "France",
  "Italy",
  "Spain",
  "Netherlands",
  "Switzerland",
  "Sweden",
  "Norway",
  "Denmark",
  "Finland",
  "Belgium",
  "Austria",
  "Ireland",
  "New Zealand",
  "Japan",
  "South Korea",
  "Singapore",
  "India",
  "China",
  "Brazil",
  "Mexico",
  "Argentina",
  "Chile",
  "South Africa",
  "Egypt",
  "Morocco",
  "Kenya",
  "Nigeria",
  "Ghana",
  "Ethiopia",
  "Tanzania",
].sort();

// Gender options
const GENDER_OPTIONS = [
  { value: "male", label: "Male" },
  { value: "female", label: "Female" },
  { value: "other", label: "Other" },
];

// Relationship options for additional travellers
const RELATIONSHIP_OPTIONS = [
  "spouse",
  "partner",
  "child",
  "parent",
  "sibling",
  "friend",
  "colleague",
  "other",
];

interface TravellerInfo {
  primary_contact: {
    name: string;
    email: string;
    dob?: string;
    country?: string;
    gender?: "male" | "female" | "other";
  };
  travellers: Array<{
    name: string;
    dob: string;
    gender?: "male" | "female" | "other";
    relationship?: string;
  }>;
}

interface TravellerInfoFormProps {
  control: Control<any>;
  name: string;
  className?: string;
}

export function TravellerInfoForm({
  control,
  name,
  className = "",
}: TravellerInfoFormProps) {
  const [isValidating, setIsValidating] = useState(false);

  // Use field array for travellers
  const {
    fields: travellers,
    append,
    remove,
  } = useFieldArray({
    control,
    name: `${name}.travellers`,
  });

  // Watch the form values for validation
  const watchedValues = useWatch({
    control,
    name,
  });

  // Validation functions
  const validateEmail = (email: string): boolean => {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return emailRegex.test(email);
  };

  const validateName = (name: string): boolean => {
    // Alphabetic only, no numbers or special characters
    const nameRegex = /^[a-zA-Z\s]+$/;
    return nameRegex.test(name.trim()) && name.trim().length > 0;
  };

  const validateDOB = (dob: string): boolean => {
    if (!dob) return false;
    const date = new Date(dob);
    const today = new Date();
    return date <= today && !isNaN(date.getTime());
  };

  const getAgeFromDOB = (dob: string): number => {
    const birthDate = new Date(dob);
    const today = new Date();
    let age = today.getFullYear() - birthDate.getFullYear();
    const monthDiff = today.getMonth() - birthDate.getMonth();

    if (
      monthDiff < 0 ||
      (monthDiff === 0 && today.getDate() < birthDate.getDate())
    ) {
      age--;
    }

    return age;
  };

  const getAgeClassification = (dob: string): string => {
    const age = getAgeFromDOB(dob);

    if (age <= AGE_CLASSIFICATIONS.INFANT.max)
      return AGE_CLASSIFICATIONS.INFANT.label;
    if (age <= AGE_CLASSIFICATIONS.CHILD.max)
      return AGE_CLASSIFICATIONS.CHILD.label;
    return AGE_CLASSIFICATIONS.ADULT.label;
  };

  const checkDuplicateTraveller = (
    name: string,
    dob: string,
    excludeIndex?: number,
  ): boolean => {
    const primaryContact = watchedValues?.primary_contact;
    const travellers = watchedValues?.travellers || [];

    // Check against primary contact
    if (primaryContact?.name === name && primaryContact?.dob === dob) {
      return true;
    }

    // Check against other travellers
    return travellers.some((traveller: any, index: number) => {
      if (excludeIndex !== undefined && index === excludeIndex) return false;
      return traveller.name === name && traveller.dob === dob;
    });
  };

  const validateForm = (): { isValid: boolean; errors: string[] } => {
    const errors: string[] = [];
    const primaryContact = watchedValues?.primary_contact;
    const travellers = watchedValues?.travellers || [];

    // Validate primary contact
    if (!primaryContact?.name || !validateName(primaryContact.name)) {
      errors.push(
        "Primary contact name is required and must contain only letters",
      );
    }

    if (!primaryContact?.email || !validateEmail(primaryContact.email)) {
      errors.push("Primary contact email is required and must be valid");
    }

    if (primaryContact?.dob && !validateDOB(primaryContact.dob)) {
      errors.push("Primary contact date of birth cannot be in the future");
    }

    // Validate travellers
    travellers.forEach((traveller: any, index: number) => {
      if (!traveller.name || !validateName(traveller.name)) {
        errors.push(
          `Traveller ${index + 1} name is required and must contain only letters`,
        );
      }

      if (!traveller.dob || !validateDOB(traveller.dob)) {
        errors.push(
          `Traveller ${index + 1} date of birth is required and cannot be in the future`,
        );
      }

      if (
        traveller.name &&
        traveller.dob &&
        checkDuplicateTraveller(traveller.name, traveller.dob, index)
      ) {
        errors.push(
          `Traveller ${index + 1} has duplicate name and date of birth`,
        );
      }
    });

    // Check for duplicates between primary contact and travellers
    if (primaryContact?.name && primaryContact?.dob) {
      travellers.forEach((traveller: any, index: number) => {
        if (
          traveller.name === primaryContact.name &&
          traveller.dob === primaryContact.dob
        ) {
          errors.push(
            `Traveller ${index + 1} has the same name and date of birth as the primary contact`,
          );
        }
      });
    }

    return { isValid: errors.length === 0, errors };
  };

  const handleAddTraveller = () => {
    if (travellers.length >= 10) {
      toast.error("Maximum 10 additional travellers allowed");
      return;
    }

    append({
      name: "",
      dob: "",
    });
  };

  const handleRemoveTraveller = (index: number) => {
    remove(index);
    toast.success("Traveller removed");
  };

  // Auto-validate on form changes
  useEffect(() => {
    if (watchedValues) {
      setIsValidating(true);
      const validation = validateForm();

      if (!validation.isValid) {
        console.log("Traveller info validation errors:", validation.errors);
      }

      setIsValidating(false);
    }
  }, [watchedValues]);

  return (
    <TooltipProvider>
      <div className={`space-y-6 ${className}`}>
        {/* Primary Contact Section */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <User className="h-5 w-5" />
              Primary Contact
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            {/* Pre-filled data indicator */}
            {watchedValues?.primary_contact?.name &&
              watchedValues?.primary_contact?.email && (
                <div className="p-3 bg-blue-50 border border-blue-200 rounded-lg">
                  <div className="flex items-center gap-2">
                    <div className="w-2 h-2 bg-blue-500 rounded-full"></div>
                    <p className="text-sm text-blue-700">
                      Primary contact pre-filled from CRM data
                    </p>
                  </div>
                </div>
              )}

            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              {/* Full Name */}
              <div className="space-y-2">
                <Label htmlFor="primary-name">Full Name *</Label>
                <Input
                  id="primary-name"
                  placeholder="Enter full name"
                  {...control.register(`${name}.primary_contact.name`)}
                  className={
                    !validateName(watchedValues?.primary_contact?.name || "") &&
                    watchedValues?.primary_contact?.name
                      ? "border-red-500"
                      : ""
                  }
                  aria-describedby="primary-name-error"
                />
                {!validateName(watchedValues?.primary_contact?.name || "") &&
                  watchedValues?.primary_contact?.name && (
                    <p id="primary-name-error" className="text-sm text-red-500">
                      Name must contain only letters
                    </p>
                  )}
              </div>

              {/* Email */}
              <div className="space-y-2">
                <Label htmlFor="primary-email">Email *</Label>
                <Input
                  id="primary-email"
                  type="email"
                  placeholder="Enter email address"
                  {...control.register(`${name}.primary_contact.email`)}
                  readOnly
                  className={`bg-gray-50 ${
                    !validateEmail(
                      watchedValues?.primary_contact?.email || "",
                    ) && watchedValues?.primary_contact?.email
                      ? "border-red-500"
                      : ""
                  }`}
                  aria-describedby="primary-email-error"
                />
                <p className="text-xs text-gray-500">
                  Email is read-only and comes from the primary contact
                </p>
                {!validateEmail(watchedValues?.primary_contact?.email || "") &&
                  watchedValues?.primary_contact?.email && (
                    <p
                      id="primary-email-error"
                      className="text-sm text-red-500"
                    >
                      Please enter a valid email address
                    </p>
                  )}
              </div>

              {/* Date of Birth */}
              <div className="space-y-2">
                <div className="flex items-center gap-2">
                  <Label htmlFor="primary-dob">Date of Birth (Optional)</Label>
                  <Tooltip>
                    <TooltipTrigger asChild>
                      <HelpCircle className="h-4 w-4 text-gray-400 cursor-help" />
                    </TooltipTrigger>
                    <TooltipContent>
                      <p className="max-w-xs">
                        Useful for travel insurance, visa processing, and
                        age-specific pricing
                      </p>
                    </TooltipContent>
                  </Tooltip>
                </div>
                <Input
                  id="primary-dob"
                  type="date"
                  {...control.register(`${name}.primary_contact.dob`)}
                  className={
                    watchedValues?.primary_contact?.dob &&
                    !validateDOB(watchedValues.primary_contact.dob)
                      ? "border-red-500"
                      : ""
                  }
                  aria-describedby="primary-dob-info"
                />
                {watchedValues?.primary_contact?.dob &&
                  validateDOB(watchedValues.primary_contact.dob) && (
                    <Badge variant="secondary" className="text-xs">
                      {getAgeClassification(watchedValues.primary_contact.dob)}{" "}
                      ({getAgeFromDOB(watchedValues.primary_contact.dob)} years)
                    </Badge>
                  )}
              </div>

              {/* Country */}
              <div className="space-y-2">
                <div className="flex items-center gap-2">
                  <Label htmlFor="primary-country">Country (Optional)</Label>
                  <Tooltip>
                    <TooltipTrigger asChild>
                      <HelpCircle className="h-4 w-4 text-gray-400 cursor-help" />
                    </TooltipTrigger>
                    <TooltipContent>
                      <p className="max-w-xs">
                        Helps with visa requirements and local travel
                        arrangements
                      </p>
                    </TooltipContent>
                  </Tooltip>
                </div>
                <Select
                  onValueChange={(value) => {
                    const field = control.register(
                      `${name}.primary_contact.country`,
                    );
                    field.onChange({ target: { value } });
                  }}
                >
                  <SelectTrigger>
                    <SelectValue placeholder="Select country" />
                  </SelectTrigger>
                  <SelectContent>
                    {COUNTRIES.map((country) => (
                      <SelectItem key={country} value={country}>
                        {country}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>

              {/* Gender */}
              <div className="space-y-2">
                <div className="flex items-center gap-2">
                  <Label htmlFor="primary-gender">Gender (Optional)</Label>
                  <Tooltip>
                    <TooltipTrigger asChild>
                      <HelpCircle className="h-4 w-4 text-gray-400 cursor-help" />
                    </TooltipTrigger>
                    <TooltipContent>
                      <p className="max-w-xs">
                        Optional field for travel documentation and arrangements
                      </p>
                    </TooltipContent>
                  </Tooltip>
                </div>
                <Select
                  onValueChange={(value) => {
                    const field = control.register(
                      `${name}.primary_contact.gender`,
                    );
                    field.onChange({ target: { value } });
                  }}
                >
                  <SelectTrigger>
                    <SelectValue placeholder="Select gender" />
                  </SelectTrigger>
                  <SelectContent>
                    {GENDER_OPTIONS.map((option) => (
                      <SelectItem key={option.value} value={option.value}>
                        {option.label}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Additional Travellers Section */}
        <Card>
          <CardHeader>
            <div className="flex items-center justify-between">
              <div className="flex items-center gap-2">
                <CardTitle className="flex items-center gap-2">
                  <Users className="h-5 w-5" />
                  Additional Travellers
                </CardTitle>
                <Badge variant="secondary" className="text-sm">
                  {travellers.length}/10 travellers
                </Badge>
              </div>
            </div>
          </CardHeader>
          <CardContent className="space-y-4">
            {travellers.length === 0 ? (
              <div className="text-center py-6 text-gray-500">
                <Users className="h-10 w-10 mx-auto mb-3 text-gray-300" />
                <p className="font-medium mb-1">
                  No additional travellers added yet
                </p>
                <p className="text-sm">
                  Click "Add Traveller" to include co-travellers
                </p>
              </div>
            ) : (
              <div className="space-y-4">
                {/* Traveller Summary */}
                <div className="bg-blue-50 border border-blue-200 rounded-lg p-3 mb-4">
                  <div className="flex items-center gap-2 mb-2">
                    <Info className="h-4 w-4 text-blue-600" />
                    <span className="text-sm font-medium text-blue-800">
                      Traveller Summary
                    </span>
                  </div>
                  <div className="text-sm text-blue-700">
                    {travellers.length} additional traveller
                    {travellers.length !== 1 ? "s" : ""} added
                    {travellers.some((t: any) => t.name && t.dob) && (
                      <span className="ml-2">
                        •{" "}
                        {travellers.filter((t: any) => t.name && t.dob).length}{" "}
                        with complete information
                      </span>
                    )}
                  </div>
                </div>

                {travellers.map((traveller: any, index: number) => (
                  <div
                    key={traveller.id}
                    className="border rounded-lg p-4 bg-gray-50"
                  >
                    <div className="flex items-center justify-between mb-4">
                      <h4 className="font-medium">Traveller {index + 1}</h4>
                      <Button
                        type="button"
                        variant="ghost"
                        size="sm"
                        onClick={() => handleRemoveTraveller(index)}
                        className="text-red-500 hover:text-red-700"
                        aria-label={`Remove traveller ${index + 1}`}
                      >
                        <Trash2 className="h-4 w-4" />
                      </Button>
                    </div>

                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                      {/* Full Name */}
                      <div className="space-y-2">
                        <Label htmlFor={`traveller-${index}-name`}>
                          Full Name *
                        </Label>
                        <Input
                          id={`traveller-${index}-name`}
                          placeholder="Enter full name"
                          {...control.register(
                            `${name}.travellers.${index}.name`,
                          )}
                          className={
                            !validateName(traveller.name || "") &&
                            traveller.name
                              ? "border-red-500"
                              : ""
                          }
                          aria-describedby={`traveller-${index}-name-error`}
                        />
                        {!validateName(traveller.name || "") &&
                          traveller.name && (
                            <p
                              id={`traveller-${index}-name-error`}
                              className="text-sm text-red-500"
                            >
                              Name must contain only letters
                            </p>
                          )}
                      </div>

                      {/* Date of Birth */}
                      <div className="space-y-2">
                        <Label htmlFor={`traveller-${index}-dob`}>
                          Date of Birth *
                        </Label>
                        <Input
                          id={`traveller-${index}-dob`}
                          type="date"
                          {...control.register(
                            `${name}.travellers.${index}.dob`,
                          )}
                          className={
                            !validateDOB(traveller.dob || "") && traveller.dob
                              ? "border-red-500"
                              : ""
                          }
                          aria-describedby={`traveller-${index}-dob-info`}
                        />
                        {traveller.dob && validateDOB(traveller.dob) && (
                          <Badge variant="secondary" className="text-xs">
                            {getAgeClassification(traveller.dob)} (
                            {getAgeFromDOB(traveller.dob)} years)
                          </Badge>
                        )}
                      </div>

                      {/* Gender */}
                      <div className="space-y-2">
                        <Label htmlFor={`traveller-${index}-gender`}>
                          Gender (Optional)
                        </Label>
                        <Select
                          onValueChange={(value) => {
                            const field = control.register(
                              `${name}.travellers.${index}.gender`,
                            );
                            field.onChange({ target: { value } });
                          }}
                        >
                          <SelectTrigger>
                            <SelectValue placeholder="Select gender" />
                          </SelectTrigger>
                          <SelectContent>
                            {GENDER_OPTIONS.map((option) => (
                              <SelectItem key={option.value} value={option.value}>
                                {option.label}
                              </SelectItem>
                            ))}
                          </SelectContent>
                        </Select>
                      </div>

                      {/* Relationship */}
                      <div className="space-y-2">
                        <Label htmlFor={`traveller-${index}-relationship`}>
                          Relationship (Optional)
                        </Label>
                        <Select
                          onValueChange={(value) => {
                            const field = control.register(
                              `${name}.travellers.${index}.relationship`,
                            );
                            field.onChange({ target: { value } });
                          }}
                        >
                          <SelectTrigger>
                            <SelectValue placeholder="Select relationship" />
                          </SelectTrigger>
                          <SelectContent>
                            {RELATIONSHIP_OPTIONS.map((relationship) => (
                              <SelectItem key={relationship} value={relationship}>
                                {relationship.charAt(0).toUpperCase() + relationship.slice(1)}
                              </SelectItem>
                            ))}
                          </SelectContent>
                        </Select>
                      </div>
                    </div>

                    {/* Duplicate warning */}
                    {traveller.name &&
                      traveller.dob &&
                      checkDuplicateTraveller(
                        traveller.name,
                        traveller.dob,
                        index,
                      ) && (
                        <div className="mt-2 p-2 bg-red-50 border border-red-200 rounded text-red-700 text-sm">
                          ⚠️ This traveller has the same name and date of birth
                          as another person
                        </div>
                      )}
                  </div>
                ))}
              </div>
            )}

            <Button
              type="button"
              variant="outline"
              onClick={handleAddTraveller}
              disabled={travellers.length >= 10}
              className="w-full"
              aria-label="Add new traveller"
            >
              <Plus className="h-4 w-4 mr-2" />
              Add Traveller
            </Button>
          </CardContent>
        </Card>

        {/* Validation Summary */}
        {isValidating && (
          <div className="p-4 bg-blue-50 border border-blue-200 rounded-lg">
            <p className="text-sm text-blue-700">
              Validating traveller information...
            </p>
          </div>
        )}
      </div>
    </TooltipProvider>
  );
}
