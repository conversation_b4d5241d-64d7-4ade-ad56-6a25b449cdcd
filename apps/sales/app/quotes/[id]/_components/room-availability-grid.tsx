"use client";

import React, { useState } from "react";
import { cn } from "@flinkk/lib/cn";
import {
  format,
  eachDayOfInterval,
  startOfMonth,
  endOfMonth,
  differenceInDays,
} from "date-fns";
import {
  AvailabilityData,
  Room,
  RoomBlock,
  PricingData,
  ResolvedPrice,
  GuestDetails,
} from "./hotel-booking-calendar";
import { OccupancyConfig } from "@flinkk/inventory-api";

interface RoomAvailabilityGridProps {
  availabilityData: AvailabilityData;
  pricingData: PricingData | null;
  filteredRooms: Room[];
  currentMonth: Date;
  roomBlocks: RoomBlock[];
  onRoomBlockAdd: (roomBlock: RoomBlock) => void;
  guestDetails?: GuestDetails;
  selectedMealPlan?: string;
}

interface DateSelection {
  roomId: string;
  checkIn: string | null;
  checkOut: string | null;
}

// Enhanced pricing result with breakdown
interface GuestBasedPricing {
  totalPrice: number;
  breakdown: {
    base: number;
    extraAdults: number;
    children: number;
  };
  type: "base" | "seasonal";
  rule_name: string;
  available: boolean;
  error?: string;
}

interface OccupancyValidationResult {
  valid: boolean;
  errors: string[];
  warnings: string[];
}

interface OccupancyUsage {
  BASE_1: number;
  BASE_2: number;
  EXTRA_ADULT: number;
  EXTRA_ADULT_BEYOND_CAPACITY: number;
  CHILD: number;
  INFANT: number;
}

/**
 * Validates guest capacity against room occupancy configuration limits
 */
function validateGuestCapacity(
  guestDetails: GuestDetails,
  occupancyConfigs: OccupancyConfig[],
  roomCapacity?: number,
): OccupancyValidationResult {
  const { adults, children } = guestDetails;
  const totalGuests = adults + children;
  const errors: string[] = [];
  const warnings: string[] = [];

  // 1. Total guest count check
  if (roomCapacity && totalGuests > roomCapacity) {
    errors.push(
      `Room cannot accommodate ${totalGuests} guests (maximum: ${roomCapacity})`,
    );
  }

  // 2. Create occupancy usage mapping
  const occupancyUsage: OccupancyUsage = {
    BASE_1: adults >= 1 ? 1 : 0,
    BASE_2: adults === 2 ? 1 : 0, // Only count BASE_2 if exactly 2 adults
    EXTRA_ADULT: Math.max(0, adults - 1), // Extra adults beyond first
    EXTRA_ADULT_BEYOND_CAPACITY: 0, // Will be calculated if needed
    CHILD: children,
    INFANT: 0, // Not currently supported in guest form
  };

  // 3. Create occupancy rules map from configs
  const occupancyRules: Record<string, number> = {};
  occupancyConfigs.forEach((config) => {
    occupancyRules[config.type] = config.max_occupancy;
  });

  console.log("🔍 DEBUG: Occupancy validation:", {
    guestDetails,
    occupancyUsage,
    occupancyRules,
    totalGuests,
    roomCapacity,
  });

  // 4. Validate each occupancy type against its limit
  Object.entries(occupancyUsage).forEach(([type, count]) => {
    const maxAllowed = occupancyRules[type];
    if (maxAllowed !== undefined && count > maxAllowed) {
      const configName =
        occupancyConfigs.find((c) => c.type === type)?.name || type;
      errors.push(
        `${configName} exceeds allowed limit (${count} > ${maxAllowed})`,
      );
    }
  });

  return {
    valid: errors.length === 0,
    errors,
    warnings,
  };
}

/**
 * Gets user-friendly error message for capacity validation
 */
function getCapacityErrorMessage(
  validation: OccupancyValidationResult,
): string {
  if (validation.valid) return "";

  if (validation.errors.length === 1) {
    return validation.errors[0];
  }

  return `Multiple capacity issues: ${validation.errors.join("; ")}`;
}

/**
 * Validates guest capacity across all available rooms to provide global feedback
 */
export function validateGuestCapacityForHotel(
  guestDetails: GuestDetails,
  pricingData: PricingData | null,
  availabilityData: AvailabilityData | null,
): {
  hasValidRooms: boolean;
  globalErrors: string[];
  globalWarnings: string[];
  roomValidations: Record<string, OccupancyValidationResult>;
} {
  const globalErrors: string[] = [];
  const globalWarnings: string[] = [];
  const roomValidations: Record<string, OccupancyValidationResult> = {};
  let hasValidRooms = false;

  if (!pricingData || !availabilityData) {
    return {
      hasValidRooms: false,
      globalErrors: ["Hotel data not available"],
      globalWarnings: [],
      roomValidations: {},
    };
  }

  // Check each room configuration
  availabilityData.rooms.forEach((room) => {
    const roomConfig = availabilityData.room_configs.find(
      (config) => config.id === room.product_id,
    );
    const roomCapacity = roomConfig?.capacity;

    const validation = validateGuestCapacity(
      guestDetails,
      pricingData.pricing.occupancy_configs,
      roomCapacity,
    );

    roomValidations[room.id] = validation;

    if (validation.valid) {
      hasValidRooms = true;
    }
  });

  // Generate global feedback
  if (!hasValidRooms) {
    const { adults, children } = guestDetails;
    const totalGuests = adults + children;

    if (totalGuests === 0) {
      globalErrors.push("Please specify at least 1 adult");
    } else {
      globalErrors.push(
        "No rooms available for the selected guest configuration",
      );

      // Provide helpful suggestions
      const maxCapacity = Math.max(
        ...availabilityData.room_configs.map((c) => c.capacity || 0),
      );
      if (totalGuests > maxCapacity) {
        globalErrors.push(`Maximum room capacity is ${maxCapacity} guests`);
      }

      // Check occupancy config limits
      const occupancyLimits = pricingData.pricing.occupancy_configs.reduce(
        (acc, config) => {
          acc[config.type] = config.max_occupancy;
          return acc;
        },
        {} as Record<string, number>,
      );

      if (
        adults > 1 &&
        (!occupancyLimits.EXTRA_ADULT ||
          adults - 1 > occupancyLimits.EXTRA_ADULT)
      ) {
        globalErrors.push(
          `Maximum ${occupancyLimits.EXTRA_ADULT || 0} extra adults allowed`,
        );
      }

      if (
        children > 0 &&
        (!occupancyLimits.CHILD || children > occupancyLimits.CHILD)
      ) {
        globalErrors.push(
          `Maximum ${occupancyLimits.CHILD || 0} children allowed`,
        );
      }
    }
  }

  return { hasValidRooms, globalErrors, globalWarnings, roomValidations };
}

// Enhanced utility function to resolve guest-based pricing for a specific room and date
function resolveGuestBasedPricing(
  roomConfigId: string,
  date: string,
  pricingData: PricingData | null,
  guestDetails: GuestDetails,
  roomCapacity?: number,
): GuestBasedPricing | null {
  console.log("🔍 DEBUG: resolveGuestBasedPricing called with:", {
    roomConfigId,
    date,
    guestDetails,
    roomCapacity,
    pricingDataExists: !!pricingData,
    pricingDataSuccess: pricingData?.success,
  });

  if (!pricingData || !pricingData.success) {
    console.log("❌ DEBUG: No pricing data or pricing data not successful");
    return null;
  }

  // Debug pricing data structure (only log once per call)
  console.log("📊 DEBUG: Pricing data structure:", {
    success: pricingData.success,
    hotel: pricingData.hotel || "No hotel info",
    basePriceRulesCount: pricingData.pricing?.base_price_rules?.length || 0,
    seasonalPriceRulesCount:
      pricingData.pricing?.seasonal_price_rules?.length || 0,
    occupancyConfigsCount: pricingData.pricing?.occupancy_configs?.length || 0,
    mealPlansCount: pricingData.pricing?.meal_plans?.length || 0,
    currencyCode: pricingData.pricing?.currency_code,
  });

  const { adults, children } = guestDetails;

  // Validate guest capacity using occupancy configurations
  const capacityValidation = validateGuestCapacity(
    guestDetails,
    pricingData.pricing.occupancy_configs,
    roomCapacity,
  );

  if (!capacityValidation.valid) {
    const errorMessage = getCapacityErrorMessage(capacityValidation);
    console.log("❌ DEBUG: Guest capacity validation failed", {
      guestDetails,
      roomCapacity,
      errors: capacityValidation.errors,
      warnings: capacityValidation.warnings,
    });
    return {
      totalPrice: 0,
      breakdown: { base: 0, extraAdults: 0, children: 0 },
      type: "base",
      rule_name: "Capacity Exceeded",
      available: false,
      error: errorMessage,
    };
  }

  // Log any warnings
  if (capacityValidation.warnings.length > 0) {
    console.log("⚠️ DEBUG: Capacity warnings", {
      warnings: capacityValidation.warnings,
    });
  }

  const dateObj = new Date(date);
  const dayOfWeek = dateObj
    .toLocaleDateString("en-US", { weekday: "long" })
    .toLowerCase();

  // Map day names to price field keys
  const dayMap: Record<string, string> = {
    sunday: "sunday_price",
    monday: "monday_price",
    tuesday: "tuesday_price",
    wednesday: "wednesday_price",
    thursday: "thursday_price",
    friday: "friday_price",
    saturday: "saturday_price",
  };

  const priceField = dayMap[dayOfWeek] || "amount";
  console.log("📅 DEBUG: Date processing", { date, dayOfWeek, priceField });

  // Debug occupancy configs
  console.log(
    "🏢 DEBUG: Available occupancy configs:",
    pricingData.pricing.occupancy_configs.map((config) => ({
      id: config.id,
      name: config.name,
      type: config.type,
      title: config.title,
    })),
  );

  // Find occupancy configs for pricing calculation using correct names
  const baseOccupancy = pricingData.pricing.occupancy_configs.find(
    (config) => config.type === "BASE_1",
  );
  const base2Occupancy = pricingData.pricing.occupancy_configs.find(
    (config) => config.type === "BASE_2",
  );
  const extraAdultOccupancy = pricingData.pricing.occupancy_configs.find(
    (config) => config.type === "EXTRA_ADULT",
  );
  const extraAdultBeyondCapacityOccupancy =
    pricingData.pricing.occupancy_configs.find(
      (config) => config.type === "EXTRA_ADULT_BEYOND_CAPACITY",
    );
  const childOccupancy = pricingData.pricing.occupancy_configs.find(
    (config) => config.type === "CHILD",
  );
  const infantOccupancy = pricingData.pricing.occupancy_configs.find(
    (config) => config.type === "INFANT",
  );

  console.log("🎯 DEBUG: Found occupancy configs:", {
    baseOccupancy: baseOccupancy
      ? {
          id: baseOccupancy.id,
          name: baseOccupancy.name,
          type: baseOccupancy.type,
        }
      : null,
    base2Occupancy: base2Occupancy
      ? {
          id: base2Occupancy.id,
          name: base2Occupancy.name,
          type: base2Occupancy.type,
        }
      : null,
    extraAdultOccupancy: extraAdultOccupancy
      ? {
          id: extraAdultOccupancy.id,
          name: extraAdultOccupancy.name,
          type: extraAdultOccupancy.type,
        }
      : null,
    extraAdultBeyondCapacityOccupancy: extraAdultBeyondCapacityOccupancy
      ? {
          id: extraAdultBeyondCapacityOccupancy.id,
          name: extraAdultBeyondCapacityOccupancy.name,
          type: extraAdultBeyondCapacityOccupancy.type,
        }
      : null,
    childOccupancy: childOccupancy
      ? {
          id: childOccupancy.id,
          name: childOccupancy.name,
          type: childOccupancy.type,
        }
      : null,
    infantOccupancy: infantOccupancy
      ? {
          id: infantOccupancy.id,
          name: infantOccupancy.name,
          type: infantOccupancy.type,
        }
      : null,
  });

  let basePrice = 0;
  let extraAdultPrice = 0;
  let childPrice = 0;
  let ruleType: "base" | "seasonal" = "base";
  let ruleName = "Base Rate";
  let extraAdultsCount = 0;

  // Debug base price rules
  console.log(
    "💰 DEBUG: Available base price rules for room:",
    pricingData.pricing.base_price_rules
      .filter((rule) => rule.room_config_id === roomConfigId)
      .map((rule) => ({
        id: rule.id,
        room_config_id: rule.room_config_id,
        occupancy_type_id: rule.occupancy_type_id,
        amount: rule.amount,
        description: rule.description,
        [priceField]: (rule as any)[priceField],
      })),
  );

  // Check for seasonal pricing first
  const applicableSeasonalRules =
    pricingData.pricing.seasonal_price_rules.filter((rule) => {
      const startDate = new Date(rule.start_date);
      const endDate = new Date(rule.end_date);
      const currentDate = new Date(date);
      return currentDate >= startDate && currentDate <= endDate;
    });

  console.log("🌟 DEBUG: Seasonal rules check:", {
    totalSeasonalRules: pricingData.pricing.seasonal_price_rules.length,
    applicableSeasonalRules: applicableSeasonalRules.length,
    date,
  });

  if (applicableSeasonalRules.length > 0) {
    const seasonalRule = applicableSeasonalRules[0];
    ruleType = "seasonal";
    ruleName = seasonalRule.description || seasonalRule.name || "Seasonal Rate";

    // For seasonal pricing, use the seasonal amount as base price
    basePrice = parseInt(seasonalRule.amount) / 100;
    console.log("🌟 DEBUG: Using seasonal pricing:", {
      seasonalRule,
      basePrice,
    });
  } else {
    // Use base pricing rules - determine which base occupancy to use
    let selectedBaseOccupancy = null;

    if (adults === 1 && baseOccupancy) {
      selectedBaseOccupancy = baseOccupancy;
      extraAdultsCount = 0;
    } else if (adults === 2 && base2Occupancy) {
      selectedBaseOccupancy = base2Occupancy;
      extraAdultsCount = 0;
    } else if (adults >= 2 && baseOccupancy) {
      // Fall back to BASE_1 + extra adults if BASE_2 is not available
      selectedBaseOccupancy = baseOccupancy;
      extraAdultsCount = adults - 1;
    } else if (adults >= 1 && baseOccupancy) {
      // Default fallback to BASE_1
      selectedBaseOccupancy = baseOccupancy;
      extraAdultsCount = Math.max(0, adults - 1);
    }

    console.log("🔍 DEBUG: Occupancy selection logic:", {
      adults,
      selectedBaseOccupancy: selectedBaseOccupancy
        ? { name: selectedBaseOccupancy.name, type: selectedBaseOccupancy.type }
        : null,
      extraAdultsCount,
      roomConfigId,
      totalBasePriceRules: pricingData.pricing.base_price_rules.length,
    });

    const baseRule = pricingData.pricing.base_price_rules.find(
      (rule) =>
        rule.room_config_id === roomConfigId &&
        rule.occupancy_type_id === selectedBaseOccupancy?.id,
    );

    console.log(
      "🎯 DEBUG: Base rule found:",
      baseRule
        ? {
            id: baseRule.id,
            room_config_id: baseRule.room_config_id,
            occupancy_type_id: baseRule.occupancy_type_id,
            amount: baseRule.amount,
            [priceField]: (baseRule as any)[priceField],
            description: baseRule.description,
          }
        : null,
    );

    if (baseRule) {
      const priceInPence = (baseRule as any)[priceField] || baseRule.amount;
      console.log("💰 DEBUG: Base price calculation details:", {
        priceField,
        priceInPence,
        priceInPenceType: typeof priceInPence,
        priceInPenceValue: priceInPence,
        baseRuleAmount: baseRule.amount,
        baseRuleAmountType: typeof baseRule.amount,
        allPriceFields: {
          amount: baseRule.amount,
          monday_price: baseRule.monday_price,
          tuesday_price: baseRule.tuesday_price,
          wednesday_price: baseRule.wednesday_price,
          thursday_price: baseRule.thursday_price,
          friday_price: baseRule.friday_price,
          saturday_price: baseRule.saturday_price,
          sunday_price: baseRule.sunday_price,
        },
      });

      // Handle both string and number types, and ensure we have a valid number
      let numericPrice = 0;
      if (typeof priceInPence === "string") {
        numericPrice = parseInt(priceInPence) || 0;
      } else if (typeof priceInPence === "number") {
        numericPrice = priceInPence;
      }

      basePrice = numericPrice / 100;
      ruleName = baseRule.description || "Base Rate";
      console.log("💰 DEBUG: Final base price calculation:", {
        numericPrice,
        basePrice,
        ruleName,
      });
    } else {
      console.log(
        "❌ DEBUG: No base rule found for room config and occupancy type",
      );
    }
  }

  // Calculate extra adult pricing
  if (extraAdultsCount > 0 && extraAdultOccupancy) {
    const extraAdultRule = pricingData.pricing.base_price_rules.find(
      (rule) =>
        rule.room_config_id === roomConfigId &&
        rule.occupancy_type_id === extraAdultOccupancy.id,
    );

    console.log("👥 DEBUG: Extra adult pricing:", {
      extraAdultsCount,
      extraAdultOccupancy: {
        name: extraAdultOccupancy.name,
        type: extraAdultOccupancy.type,
      },
      foundRule: !!extraAdultRule,
    });

    if (extraAdultRule) {
      const priceInPence =
        (extraAdultRule as any)[priceField] || extraAdultRule.amount;
      extraAdultPrice = (parseInt(priceInPence) / 100) * extraAdultsCount;
      console.log("👥 DEBUG: Extra adult calculation:", {
        priceInPence,
        extraAdultsCount,
        extraAdultPrice,
      });
    }
  }

  // Calculate child pricing
  if (children > 0 && childOccupancy) {
    const childRule = pricingData.pricing.base_price_rules.find(
      (rule) =>
        rule.room_config_id === roomConfigId &&
        rule.occupancy_type_id === childOccupancy.id,
    );

    if (childRule) {
      const priceInPence = (childRule as any)[priceField] || childRule.amount;
      childPrice = (parseInt(priceInPence) / 100) * children;
    }
  }

  const totalPrice = basePrice + extraAdultPrice + childPrice;

  console.log("🎉 DEBUG: Final pricing calculation:", {
    basePrice,
    extraAdultPrice,
    childPrice,
    totalPrice,
    ruleType,
    ruleName,
    guestDetails,
  });

  return {
    totalPrice,
    breakdown: {
      base: basePrice,
      extraAdults: extraAdultPrice,
      children: childPrice,
    },
    type: ruleType,
    rule_name: ruleName,
    available: true,
  };
}

// Legacy utility function to resolve pricing for a specific room and date (kept for compatibility)
function resolvePricing(
  roomConfigId: string,
  date: string,
  pricingData: PricingData | null,
  occupancyTypeId?: string,
  mealPlanId?: string,
): ResolvedPrice | null {
  if (!pricingData || !pricingData.success) return null;

  const dateObj = new Date(date);
  const dayOfWeek = dateObj
    .toLocaleDateString("en-US", { weekday: "long" })
    .toLowerCase();

  // Map day names to price field keys
  const dayMap: Record<string, string> = {
    sunday: "sunday_price",
    monday: "monday_price",
    tuesday: "tuesday_price",
    wednesday: "wednesday_price",
    thursday: "thursday_price",
    friday: "friday_price",
    saturday: "saturday_price",
  };

  const priceField = dayMap[dayOfWeek] || "monday_price";

  // Check for seasonal pricing first (highest priority)
  const applicableSeasonalRules = pricingData.pricing.seasonal_price_rules
    .filter((rule) => {
      const startDate = new Date(rule.start_date);
      const endDate = new Date(rule.end_date);
      return dateObj >= startDate && dateObj <= endDate;
    })
    .sort((a, b) => b.priority - a.priority); // Sort by priority descending

  if (applicableSeasonalRules.length > 0) {
    const seasonalRule = applicableSeasonalRules[0];
    // Find the base rule to get daily pricing with occupancy and meal plan filters
    const baseRule = pricingData.pricing.base_price_rules.find(
      (rule) =>
        rule.id === seasonalRule.base_price_rule_id &&
        rule.room_config_id === roomConfigId &&
        (!occupancyTypeId || rule.occupancy_type_id === occupancyTypeId) &&
        (!mealPlanId || rule.meal_plan_id === mealPlanId),
    );

    if (baseRule) {
      // For seasonal rules, we use the seasonal amount as override
      // In a real implementation, you might have daily seasonal pricing
      const price = parseInt(seasonalRule.amount) / 100; // Convert from pence to pounds
      return {
        price,
        type: "seasonal",
        rule_name:
          seasonalRule.description || seasonalRule.name || "Seasonal Rate",
      };
    }
  }

  // Fall back to base pricing with occupancy and meal plan filters
  const baseRule = pricingData.pricing.base_price_rules.find(
    (rule) =>
      rule.room_config_id === roomConfigId &&
      (!occupancyTypeId || rule.occupancy_type_id === occupancyTypeId) &&
      (!mealPlanId || rule.meal_plan_id === mealPlanId),
  );

  if (baseRule) {
    const priceInPence = (baseRule as any)[priceField] || baseRule.amount;
    const price = parseInt(priceInPence) / 100; // Convert from pence to pounds
    return {
      price,
      type: "base",
      rule_name: baseRule.description || "Base Rate",
    };
  }

  return null;
}

// Simple tooltip component
interface TooltipProps {
  children: React.ReactNode;
  content: string;
  show: boolean;
}

function Tooltip({ children, content, show }: TooltipProps) {
  return (
    <div className="relative group">
      {children}
      {show && (
        <div
          className="absolute bottom-full left-1/2 transform -translate-x-1/2 mb-2 px-3 py-2 bg-gray-900 text-white text-xs rounded shadow-lg pointer-events-none z-[9999] group-hover:block"
          style={{
            minWidth: "max-content",
            maxWidth: "400px",
            whiteSpace: "pre-line",
            width: "auto",
          }}
        >
          {content}
          <div className="absolute top-full left-1/2 transform -translate-x-1/2 border-4 border-transparent border-t-gray-900"></div>
        </div>
      )}
    </div>
  );
}

export function RoomAvailabilityGrid({
  availabilityData,
  pricingData,
  filteredRooms,
  currentMonth,
  roomBlocks,
  onRoomBlockAdd,
  guestDetails,
  selectedMealPlan,
}: RoomAvailabilityGridProps) {
  console.log({ availabilityData });
  console.log({ pricingData });
  console.log({ filteredRooms });

  // Add debug function to window for testing
  React.useEffect(() => {
    if (typeof window !== "undefined") {
      (window as any).debugGuestPricing = (
        roomConfigId: string,
        date: string,
      ) => {
        console.log("🧪 DEBUG: Manual test of guest pricing");
        const testGuestDetails = { adults: 2, children: 1 };
        return resolveGuestBasedPricing(
          roomConfigId,
          date,
          pricingData,
          testGuestDetails,
        );
      };

      (window as any).debugPricingData = () => {
        console.log("🧪 DEBUG: Current pricing data", pricingData);
        return pricingData;
      };
    }
  }, [pricingData]);

  const [dateSelection, setDateSelection] = useState<DateSelection>({
    roomId: "",
    checkIn: null,
    checkOut: null,
  });

  const [hoveredCell, setHoveredCell] = useState<{
    roomId: string;
    date: string;
  } | null>(null);

  // Helper function to check if a room is available on a specific date
  const checkRoomAvailability = (roomId: string, date: string): boolean => {
    if (
      !availabilityData.availability ||
      !Array.isArray(availabilityData.availability)
    ) {
      return false;
    }

    // Find availability record for this room and date
    const availabilityRecord = availabilityData.availability.find(
      (record: any) =>
        record.room_id === roomId &&
        new Date(record.from_date) <= new Date(date) &&
        new Date(record.to_date) >= new Date(date) &&
        record.status === "available",
    );

    return !!availabilityRecord;
  };

  // Generate dates for the current month
  const monthStart = startOfMonth(currentMonth);
  const monthEnd = endOfMonth(currentMonth);
  const dates = eachDayOfInterval({ start: monthStart, end: monthEnd });

  const handleDateClick = (roomId: string, date: string) => {
    console.log({ roomId, date });
    console.log({ filteredRooms });
    const room = filteredRooms.find((r) => r.id === roomId);
    if (!room) return;

    // Check if date is available
    const isAvailable = checkRoomAvailability(roomId, date);
    console.log({ isAvailable });
    if (!isAvailable) return;

    console.log(dateSelection);

    // If no selection or different room, start new selection
    if (!dateSelection.checkIn || dateSelection.roomId !== roomId) {
      setDateSelection({
        roomId,
        checkIn: date,
        checkOut: null,
      });
      return;
    }

    // If same room and we have check-in, set check-out
    if (dateSelection.checkIn && !dateSelection.checkOut) {
      const checkInDate = new Date(dateSelection.checkIn);
      const checkOutDate = new Date(date);

      // Ensure check-out is after check-in
      if (checkOutDate <= checkInDate) {
        setDateSelection({
          roomId,
          checkIn: date,
          checkOut: null,
        });
        return;
      }

      // Check if all dates in range are available
      const rangeStart = new Date(dateSelection.checkIn);
      const rangeEnd = new Date(date);
      const rangeDates = eachDayOfInterval({
        start: rangeStart,
        end: rangeEnd,
      });

      const allAvailable = rangeDates.every((d) => {
        const dateStr = format(d, "yyyy-MM-dd");
        return checkRoomAvailability(roomId, dateStr);
      });

      if (!allAvailable) {
        // Show error or reset selection
        setDateSelection({
          roomId,
          checkIn: date,
          checkOut: null,
        });
        return;
      }

      // Create room block with accurate pricing
      const config = availabilityData.room_configs.find(
        (c) => c.id === room.product_id,
      );
      const nights = differenceInDays(checkOutDate, checkInDate);

      // Calculate total based on daily pricing (guest-based if available)
      let total = 0;
      let averageRate = 0;

      if (pricingData) {
        for (let i = 0; i < nights; i++) {
          const currentDate = new Date(checkInDate);
          currentDate.setDate(currentDate.getDate() + i);
          const dateStr = format(currentDate, "yyyy-MM-dd");

          // Use guest-based pricing if guest details are available
          if (guestDetails) {
            const guestPricing = resolveGuestBasedPricing(
              room.product_id,
              dateStr,
              pricingData,
              guestDetails,
              config?.capacity,
            );
            if (guestPricing && guestPricing.available) {
              total += guestPricing.totalPrice;
            }
          } else {
            // Fallback to legacy pricing
            const pricing = resolvePricing(
              room.product_id,
              dateStr,
              pricingData,
              undefined, // occupancyTypeId
              selectedMealPlan,
            );
            if (pricing) {
              total += pricing.price;
            }
          }
        }
        averageRate = nights > 0 ? total / nights : 0;
      } else {
        // Fallback to base price if pricing data not available
        averageRate = config?.basePrice || 0;
        total = averageRate * nights;
      }

      const roomBlock: RoomBlock = {
        id: crypto.randomUUID(),
        room_id: roomId,
        room_name: room.room_number || room.room_name, // Use room_number if available, fallback to room_name
        product_id: room.product_id,
        config_name: config?.title || "",
        check_in: dateSelection.checkIn,
        check_out: date,
        nights,
        rate: averageRate,
        total,
        meal_plan_id: selectedMealPlan,
      };

      onRoomBlockAdd(roomBlock);

      // Reset selection
      setDateSelection({
        roomId: "",
        checkIn: null,
        checkOut: null,
      });
    }
  };

  const isDateInSelection = (roomId: string, date: string) => {
    // Check if date is in any existing room block for this room
    const isInExistingBlock = roomBlocks.some((block: RoomBlock) => {
      if (block.room_id !== roomId) return false;

      const dateObj = new Date(date);
      const checkIn = new Date(block.check_in);
      const checkOut = new Date(block.check_out);

      return dateObj >= checkIn && dateObj < checkOut;
    });

    if (isInExistingBlock) return true;

    // Check if date is in current selection
    if (dateSelection.roomId !== roomId || !dateSelection.checkIn) return false;

    if (!dateSelection.checkOut) {
      return date === dateSelection.checkIn;
    }

    const dateObj = new Date(date);
    const checkIn = dateSelection.checkIn
      ? new Date(dateSelection.checkIn)
      : null;
    const checkOut = dateSelection.checkOut
      ? new Date(dateSelection.checkOut)
      : null;

    if (!checkIn || !checkOut) return false;

    return dateObj >= checkIn && dateObj < checkOut;
  };

  const getDateCellClass = (
    roomId: string,
    date: string,
    forceUnavailable?: boolean,
  ) => {
    const baseAvailable = checkRoomAvailability(roomId, date);
    const isAvailable = baseAvailable && !forceUnavailable;
    const isSelected = isDateInSelection(roomId, date);
    const isCheckIn =
      dateSelection.roomId === roomId && dateSelection.checkIn === date;
    const isCheckOut =
      dateSelection.roomId === roomId && dateSelection.checkOut === date;

    return cn(
      "h-8 w-8 text-xs border border-gray-200 cursor-pointer transition-colors duration-200 flex items-center justify-center",
      {
        "bg-green-100 hover:bg-green-200 text-green-800":
          isAvailable && !isSelected,
        "bg-red-100 text-red-800 cursor-not-allowed": !isAvailable,
        "bg-orange-100 text-orange-800 cursor-not-allowed":
          forceUnavailable && baseAvailable,
        "bg-blue-200 text-blue-800": isSelected,
        "bg-blue-500 text-white": isCheckIn,
        "bg-blue-600 text-white": isCheckOut,
      },
    );
  };

  if (filteredRooms.length === 0) {
    return (
      <div className="text-center py-8 text-muted-foreground">
        No rooms available for the selected configuration
      </div>
    );
  }

  return (
    <div className="space-y-4">
      {/* Legend */}
      <div className="flex items-center gap-4 text-xs">
        <div className="flex items-center gap-1">
          <div className="w-4 h-4 bg-green-100 border border-gray-200"></div>
          <span>Available</span>
        </div>
        <div className="flex items-center gap-1">
          <div className="w-4 h-4 bg-red-100 border border-gray-200"></div>
          <span>Unavailable</span>
        </div>
        <div className="flex items-center gap-1">
          <div className="w-4 h-4 bg-blue-200 border border-gray-200"></div>
          <span>Selected Range</span>
        </div>
        <div className="flex items-center gap-1">
          <div className="w-4 h-4 bg-blue-500 border border-gray-200"></div>
          <span>Check-in</span>
        </div>
      </div>

      {/* Grid Container */}
      <div
        className="overflow-x-auto overflow-y-visible"
        style={{ paddingTop: "120px", marginTop: "-120px" }}
      >
        <div className="min-w-max relative">
          {/* Date Headers */}
          <div className="flex">
            <div className="w-32 flex-shrink-0 p-2 font-medium text-sm border-b border-gray-200">
              Room
            </div>
            {dates.map((date) => (
              <div
                key={date.toISOString()}
                className="w-8 p-1 text-xs text-center font-medium border-b border-gray-200"
              >
                {format(date, "d")}
              </div>
            ))}
          </div>

          {/* Room Rows */}
          {filteredRooms.map((room) => {
            const config = availabilityData.room_configs.find(
              (c) => c.id === room.product_id,
            );

            return (
              <div key={room.id} className="flex border-b border-gray-100">
                <div className="w-32 flex-shrink-0 p-2 border-r border-gray-200">
                  <div className="text-sm font-medium">{room.room_number}</div>
                  {/* <div className="text-xs text-muted-foreground">{config?.title}</div> */}
                </div>
                {dates.map((date) => {
                  const dateStr = format(date, "yyyy-MM-dd");
                  const isAvailable = checkRoomAvailability(room.id, dateStr);

                  // Use guest-based pricing if guest details are available
                  const guestBasedPricing =
                    guestDetails && isAvailable
                      ? resolveGuestBasedPricing(
                          room.product_id,
                          dateStr,
                          pricingData,
                          guestDetails,
                          config?.capacity,
                        )
                      : null;

                  // Fallback to legacy pricing for compatibility
                  const legacyPricing =
                    !guestBasedPricing && isAvailable
                      ? resolvePricing(
                          room.product_id,
                          dateStr,
                          pricingData,
                          undefined, // occupancyTypeId
                          selectedMealPlan,
                        )
                      : null;

                  const isHovered =
                    hoveredCell?.roomId === room.id &&
                    hoveredCell?.date === dateStr;

                  const currency = pricingData?.pricing?.currency_code || "GBP";
                  const currencySymbol =
                    currency === "GBP"
                      ? "£"
                      : currency === "USD"
                        ? "$"
                        : currency === "EUR"
                          ? "€"
                          : currency;

                  // Create enhanced tooltip content with breakdown
                  let tooltipContent = "";
                  let isRoomAvailable = isAvailable;

                  if (guestBasedPricing) {
                    if (!guestBasedPricing.available) {
                      tooltipContent =
                        guestBasedPricing.error || "Room not available";
                      isRoomAvailable = false;
                    } else {
                      const { totalPrice, breakdown } = guestBasedPricing;
                      tooltipContent = `${currencySymbol}${totalPrice.toFixed(2)}`;

                      // Add breakdown if there are multiple components
                      const breakdownParts = [];
                      if (breakdown.base > 0)
                        breakdownParts.push(
                          `Base: ${currencySymbol}${breakdown.base.toFixed(2)}`,
                        );
                      if (breakdown.extraAdults > 0)
                        breakdownParts.push(
                          `Extra Adults: ${currencySymbol}${breakdown.extraAdults.toFixed(2)}`,
                        );
                      if (breakdown.children > 0)
                        breakdownParts.push(
                          `Children: ${currencySymbol}${breakdown.children.toFixed(2)}`,
                        );

                      if (breakdownParts.length > 1) {
                        tooltipContent += `\n${breakdownParts.join("\n")}`;
                      }

                      tooltipContent += `\n${guestBasedPricing.rule_name}`;
                    }
                  } else if (legacyPricing) {
                    tooltipContent = `${currencySymbol}${legacyPricing.price} – ${legacyPricing.rule_name}`;
                  }

                  return (
                    <Tooltip
                      key={dateStr}
                      content={tooltipContent}
                      show={
                        isHovered &&
                        (isRoomAvailable || !!guestBasedPricing?.error) &&
                        !!tooltipContent
                      }
                    >
                      <div
                        className={getDateCellClass(
                          room.id,
                          dateStr,
                          !isRoomAvailable,
                        )}
                        onClick={() =>
                          isRoomAvailable && handleDateClick(room.id, dateStr)
                        }
                        onMouseEnter={() =>
                          setHoveredCell({ roomId: room.id, date: dateStr })
                        }
                        onMouseLeave={() => setHoveredCell(null)}
                      >
                        {format(date, "d")}
                      </div>
                    </Tooltip>
                  );
                })}
              </div>
            );
          })}
        </div>
      </div>

      {/* Selection Instructions */}
      {dateSelection.checkIn && !dateSelection.checkOut && (
        <div className="text-sm text-blue-600 bg-blue-50 p-3 rounded-md">
          Check-in selected for {dateSelection.checkIn}. Click on an available
          date to set check-out.
        </div>
      )}
    </div>
  );
}
