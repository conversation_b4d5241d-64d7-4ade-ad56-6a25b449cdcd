import { useState } from "react";
import { toast } from "sonner";

interface TravellerInfo {
  primary_contact: {
    name: string;
    email: string;
    dob?: string;
    country?: string;
    gender?: "male" | "female" | "other";
  };
  travellers: Array<{
    name: string;
    dob: string;
    gender?: "male" | "female" | "other";
    relationship?: string;
  }>;
}

interface SaveTravellerInfoParams {
  cartId: string;
  travellerInfo: TravellerInfo;
}

interface SaveTravellerInfoResult {
  success: boolean;
  message: string;
  error?: string;
  travellerProfilesCreated?: number;
  travellerProfileErrors?: string[];
}

export function useSaveTravellerInfo() {
  const [isSaving, setIsSaving] = useState(false);

  const saveTravellerInfo = async ({
    cartId,
    travellerInfo,
  }: SaveTravellerInfoParams): Promise<SaveTravellerInfoResult> => {
    setIsSaving(true);

    try {
      console.log("🔄 Saving traveller info to cart metadata:", {
        cartId,
        travellerInfo,
      });

      // Validate traveller info
      if (
        !travellerInfo.primary_contact.name ||
        !travellerInfo.primary_contact.email
      ) {
        throw new Error("Primary contact name and email are required");
      }

      // Validate email format
      const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
      if (!emailRegex.test(travellerInfo.primary_contact.email)) {
        throw new Error("Primary contact email must be valid");
      }

      // Validate traveller names and DOBs
      const travellers = travellerInfo.travellers || [];
      for (let i = 0; i < travellers.length; i++) {
        const traveller = travellers[i];
        if (!traveller.name) {
          throw new Error(`Traveller ${i + 1} name is required`);
        }
        if (!traveller.dob) {
          throw new Error(`Traveller ${i + 1} date of birth is required`);
        }

        // Validate DOB is not in future
        const dob = new Date(traveller.dob);
        const today = new Date();
        if (dob > today) {
          throw new Error(
            `Traveller ${i + 1} date of birth cannot be in the future`,
          );
        }
      }

      // Check for duplicates
      const allNames = [
        travellerInfo.primary_contact.name,
        ...travellers.map((t) => t.name),
      ];
      const allDOBs = [
        travellerInfo.primary_contact.dob,
        ...travellers.map((t) => t.dob),
      ];

      for (let i = 0; i < allNames.length; i++) {
        for (let j = i + 1; j < allNames.length; j++) {
          if (allNames[i] === allNames[j] && allDOBs[i] === allDOBs[j]) {
            throw new Error(
              "Duplicate traveller found with same name and date of birth",
            );
          }
        }
      }

      // Prepare metadata update
      const metadataUpdate = {
        metadata: {
          traveller_info: travellerInfo,
        },
      };

      // Call the inventory API to update cart metadata
      const response = await fetch(
        `/api/hotel-management/cart/${cartId}/metadata`,
        {
          method: "PATCH",
          headers: {
            "Content-Type": "application/json",
          },
          body: JSON.stringify(metadataUpdate),
        },
      );

      if (!response.ok) {
        const errorData = await response.json().catch(() => ({}));
        throw new Error(
          errorData.message ||
            `Failed to save traveller info: ${response.status}`,
        );
      }

      const result = await response.json();

      console.log("✅ Traveller info metadata saved successfully:", result);

      // Step 2: Create traveller profiles in the inventory system
      // Note: Primary traveller should already exist from cart creation
      // We'll create additional travellers here
      let travellerProfilesCreated = 0;
      let travellerProfileErrors: string[] = [];

      if (travellerInfo.travellers && travellerInfo.travellers.length > 0) {
        console.log("🧳 Creating additional traveller profiles...");

        // First, get the cart details to extract customer_id
        try {
          const cartResponse = await fetch(`/api/traveller-profiles/list?cartId=${cartId}`);
          let customerId = null;

          if (cartResponse.ok) {
            const cartData = await cartResponse.json();
            // Try to get customer_id from existing traveller profiles
            if (cartData.traveller_profiles && cartData.traveller_profiles.length > 0) {
              customerId = cartData.traveller_profiles[0].customer_id;
            }
          }

          // If we don't have customer_id, we can't create traveller profiles
          if (!customerId) {
            console.warn("⚠️ No customer_id found, skipping traveller profile creation");
          } else {
            // Create additional traveller profiles
            for (const traveller of travellerInfo.travellers) {
              if (traveller.name && traveller.dob) {
                try {
                  // Split name into first and last name
                  const nameParts = traveller.name.trim().split(" ");
                  const firstName = nameParts[0] || "";
                  const lastName = nameParts.slice(1).join(" ") || nameParts[0] || "";

                  const travellerProfileData = {
                    cartId,
                    first_name: firstName,
                    last_name: lastName,
                    date_of_birth: traveller.dob,
                    gender: traveller.gender,
                    relationship: traveller.relationship,
                    is_primary: false,
                    customer_id: customerId,
                  };

                  const profileResponse = await fetch("/api/traveller-profiles/create", {
                    method: "POST",
                    headers: {
                      "Content-Type": "application/json",
                    },
                    body: JSON.stringify(travellerProfileData),
                  });

                  if (profileResponse.ok) {
                    travellerProfilesCreated++;
                    console.log(`✅ Created traveller profile for ${traveller.name}`);
                  } else {
                    const errorData = await profileResponse.json().catch(() => ({}));
                    const errorMsg = `Failed to create profile for ${traveller.name}: ${errorData.message || "Unknown error"}`;
                    travellerProfileErrors.push(errorMsg);
                    console.error(`❌ ${errorMsg}`);
                  }
                } catch (profileError) {
                  const errorMsg = `Error creating profile for ${traveller.name}: ${profileError instanceof Error ? profileError.message : "Unknown error"}`;
                  travellerProfileErrors.push(errorMsg);
                  console.error(`❌ ${errorMsg}`);
                }
              }
            }
          }
        } catch (error) {
          console.error("❌ Error in traveller profile creation process:", error);
          travellerProfileErrors.push("Failed to process traveller profiles");
        }
      }

      // Prepare success message
      let successMessage = "Traveller information saved successfully";
      if (travellerProfilesCreated > 0) {
        successMessage += ` (${travellerProfilesCreated} traveller profile${travellerProfilesCreated > 1 ? "s" : ""} created)`;
      }
      if (travellerProfileErrors.length > 0) {
        successMessage += `. Note: ${travellerProfileErrors.length} profile creation error${travellerProfileErrors.length > 1 ? "s" : ""}`;
      }

      toast.success(successMessage);

      return {
        success: true,
        message: successMessage,
        travellerProfilesCreated,
        travellerProfileErrors,
      };
    } catch (error) {
      console.error("❌ Error saving traveller info:", error);

      const errorMessage =
        error instanceof Error
          ? error.message
          : "Failed to save traveller information";
      toast.error(errorMessage);

      return {
        success: false,
        message: "Failed to save traveller information",
        error: errorMessage,
      };
    } finally {
      setIsSaving(false);
    }
  };

  return {
    saveTravellerInfo,
    isSaving,
  };
}
