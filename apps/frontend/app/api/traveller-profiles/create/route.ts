import { NextRequest, NextResponse } from "next/server";
import { getToken } from "@flinkk/shared-auth/token";
import { FlinkkInventoryAPI, TravellerProfileCreateRequest } from "@flinkk/inventory-api";
import { prisma } from "@flinkk/database/prisma";
import { withTenantRBAC } from "@flinkk/shared-rbac";
import { z } from "zod";

export const dynamic = "force-dynamic";

// Validation schema for traveller profile creation
const createTravellerProfileSchema = z.object({
  cartId: z.string().min(1, "Cart ID is required"),
  first_name: z.string().min(1, "First name is required"),
  last_name: z.string().min(1, "Last name is required"),
  date_of_birth: z.string().optional(),
  gender: z.enum(["male", "female", "other"]).optional(),
  relationship: z.string().optional(),
  is_primary: z.boolean(),
  customer_id: z.string().min(1, "Customer ID is required"),
});

/**
 * POST /api/traveller-profiles/create - Create a traveller profile
 * 
 * This endpoint creates a traveller profile in the inventory system for a specific cart.
 * It handles both primary and additional travellers.
 */
export const POST = withTenantRBAC(
  async (req: NextRequest, rbacPrisma: any, tenantId: string) => {
    try {
      const { userId } = await getToken({ req });

      if (!userId || !tenantId) {
        return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
      }

      // Parse and validate request body
      const body = await req.json();
      const validationResult = createTravellerProfileSchema.safeParse(body);

      if (!validationResult.success) {
        return NextResponse.json(
          {
            error: "Invalid request data",
            details: validationResult.error.errors,
          },
          { status: 400 }
        );
      }

      const { cartId, ...travellerData } = validationResult.data;

      // Get inventory configuration for the tenant
      const inventoryConfig = await prisma.inventoryConfiguration.findUnique({
        where: {
          tenantId,
        },
        select: {
          apiUrl: true,
          token: true,
          isActive: true,
          verificationStatus: true,
        },
      });

      // Check if inventory is configured and active
      if (
        !inventoryConfig ||
        !inventoryConfig.apiUrl ||
        !inventoryConfig.token
      ) {
        return NextResponse.json(
          { error: "Inventory system not configured for this tenant" },
          { status: 400 }
        );
      }

      if (!inventoryConfig.isActive) {
        return NextResponse.json(
          { error: "Inventory system is not active" },
          { status: 400 }
        );
      }

      // Create inventory API instance
      const inventoryAPI = new FlinkkInventoryAPI({
        apiUrl: inventoryConfig.apiUrl,
        token: inventoryConfig.token,
      });

      try {
        // Create traveller profile via inventory API
        const result = await inventoryAPI.createTravellerProfile(cartId, travellerData);

        console.log("✅ Traveller profile created successfully:", result);

        return NextResponse.json({
          success: true,
          message: "Traveller profile created successfully",
          traveller_profile: result.traveller_profile,
        });
      } catch (inventoryError: any) {
        console.error("❌ Inventory API error:", inventoryError);
        
        // Extract error message from inventory API response
        let errorMessage = "Failed to create traveller profile";
        if (inventoryError.message) {
          errorMessage = inventoryError.message;
        } else if (typeof inventoryError === "string") {
          errorMessage = inventoryError;
        }

        return NextResponse.json(
          { 
            error: "Failed to create traveller profile",
            message: errorMessage,
            details: inventoryError
          },
          { status: 502 }
        );
      }
    } catch (error) {
      console.error("❌ Error creating traveller profile:", error);

      const errorMessage =
        error instanceof Error
          ? error.message
          : "Failed to create traveller profile";

      return NextResponse.json(
        { 
          error: "Internal server error",
          message: errorMessage
        },
        { status: 500 }
      );
    }
  }
);
