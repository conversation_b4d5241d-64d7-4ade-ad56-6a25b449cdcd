import { NextRequest, NextResponse } from "next/server";
import { getToken } from "@flinkk/shared-auth/token";
import { FlinkkInventoryAPI } from "@flinkk/inventory-api";
import { prisma } from "@flinkk/database/prisma";
import { withTenantRBAC } from "@flinkk/shared-rbac";
import { z } from "zod";

export const dynamic = "force-dynamic";

// Validation schema for traveller profile deletion
const deleteTravellerProfileSchema = z.object({
  cartId: z.string().min(1, "Cart ID is required"),
  travellerProfileId: z.string().min(1, "Traveller profile ID is required"),
});

/**
 * DELETE /api/traveller-profiles/delete - Delete a traveller profile
 * 
 * This endpoint deletes a traveller profile from the inventory system.
 * Request body should contain: { cartId, travellerProfileId }
 */
export const DELETE = withTenantRBAC(
  async (req: NextRequest, rbacPrisma: any, tenantId: string) => {
    try {
      const { userId } = await getToken({ req });

      if (!userId || !tenantId) {
        return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
      }

      // Parse and validate request body
      const body = await req.json();
      const validationResult = deleteTravellerProfileSchema.safeParse(body);

      if (!validationResult.success) {
        return NextResponse.json(
          {
            error: "Invalid request data",
            details: validationResult.error.errors,
          },
          { status: 400 }
        );
      }

      const { cartId, travellerProfileId } = validationResult.data;

      // Get inventory configuration for the tenant
      const inventoryConfig = await prisma.inventoryConfiguration.findUnique({
        where: {
          tenantId,
        },
        select: {
          apiUrl: true,
          token: true,
          isActive: true,
          verificationStatus: true,
        },
      });

      // Check if inventory is configured and active
      if (
        !inventoryConfig ||
        !inventoryConfig.apiUrl ||
        !inventoryConfig.token
      ) {
        return NextResponse.json(
          { error: "Inventory system not configured for this tenant" },
          { status: 400 }
        );
      }

      if (!inventoryConfig.isActive) {
        return NextResponse.json(
          { error: "Inventory system is not active" },
          { status: 400 }
        );
      }

      // Create inventory API instance
      const inventoryAPI = new FlinkkInventoryAPI({
        apiUrl: inventoryConfig.apiUrl,
        token: inventoryConfig.token,
      });

      try {
        // Delete traveller profile via inventory API
        const result = await inventoryAPI.deleteTravellerProfile(cartId, travellerProfileId);

        console.log("✅ Traveller profile deleted successfully:", result);

        return NextResponse.json({
          success: true,
          message: "Traveller profile deleted successfully",
        });
      } catch (inventoryError: any) {
        console.error("❌ Inventory API error:", inventoryError);
        
        // Extract error message from inventory API response
        let errorMessage = "Failed to delete traveller profile";
        if (inventoryError.message) {
          errorMessage = inventoryError.message;
        } else if (typeof inventoryError === "string") {
          errorMessage = inventoryError;
        }

        return NextResponse.json(
          { 
            error: "Failed to delete traveller profile",
            message: errorMessage,
            details: inventoryError
          },
          { status: 502 }
        );
      }
    } catch (error) {
      console.error("❌ Error deleting traveller profile:", error);

      const errorMessage =
        error instanceof Error
          ? error.message
          : "Failed to delete traveller profile";

      return NextResponse.json(
        { 
          error: "Internal server error",
          message: errorMessage
        },
        { status: 500 }
      );
    }
  }
);
