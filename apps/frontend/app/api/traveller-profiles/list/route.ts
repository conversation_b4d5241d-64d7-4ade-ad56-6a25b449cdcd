import { NextRequest, NextResponse } from "next/server";
import { getToken } from "@flinkk/shared-auth/token";
import { FlinkkInventoryAPI } from "@flinkk/inventory-api";
import { prisma } from "@flinkk/database/prisma";
import { withTenantRBAC } from "@flinkk/shared-rbac";

export const dynamic = "force-dynamic";

/**
 * GET /api/traveller-profiles/list - List traveller profiles for a cart
 * 
 * This endpoint retrieves all traveller profiles for a specific cart from the inventory system.
 * Query parameter: cartId (required)
 */
export const GET = withTenantRBAC(
  async (req: NextRequest, rbacPrisma: any, tenantId: string) => {
    try {
      const { userId } = await getToken({ req });

      if (!userId || !tenantId) {
        return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
      }

      // Get cartId from query parameters
      const { searchParams } = new URL(req.url);
      const cartId = searchParams.get("cartId");

      if (!cartId) {
        return NextResponse.json(
          { error: "Cart ID is required" },
          { status: 400 }
        );
      }

      // Get inventory configuration for the tenant
      const inventoryConfig = await prisma.inventoryConfiguration.findUnique({
        where: {
          tenantId,
        },
        select: {
          apiUrl: true,
          token: true,
          isActive: true,
          verificationStatus: true,
        },
      });

      // Check if inventory is configured and active
      if (
        !inventoryConfig ||
        !inventoryConfig.apiUrl ||
        !inventoryConfig.token
      ) {
        return NextResponse.json(
          { error: "Inventory system not configured for this tenant" },
          { status: 400 }
        );
      }

      if (!inventoryConfig.isActive) {
        return NextResponse.json(
          { error: "Inventory system is not active" },
          { status: 400 }
        );
      }

      // Create inventory API instance
      const inventoryAPI = new FlinkkInventoryAPI({
        apiUrl: inventoryConfig.apiUrl,
        token: inventoryConfig.token,
      });

      try {
        // List traveller profiles via inventory API
        const result = await inventoryAPI.listTravellerProfiles(cartId);

        console.log("✅ Traveller profiles listed successfully:", result);

        return NextResponse.json({
          success: true,
          message: "Traveller profiles retrieved successfully",
          traveller_profiles: result.traveller_profiles,
        });
      } catch (inventoryError: any) {
        console.error("❌ Inventory API error:", inventoryError);
        
        // Extract error message from inventory API response
        let errorMessage = "Failed to list traveller profiles";
        if (inventoryError.message) {
          errorMessage = inventoryError.message;
        } else if (typeof inventoryError === "string") {
          errorMessage = inventoryError;
        }

        return NextResponse.json(
          { 
            error: "Failed to list traveller profiles",
            message: errorMessage,
            details: inventoryError
          },
          { status: 502 }
        );
      }
    } catch (error) {
      console.error("❌ Error listing traveller profiles:", error);

      const errorMessage =
        error instanceof Error
          ? error.message
          : "Failed to list traveller profiles";

      return NextResponse.json(
        { 
          error: "Internal server error",
          message: errorMessage
        },
        { status: 500 }
      );
    }
  }
);
