import { NextRequest, NextResponse } from "next/server";
import { getToken } from "@flinkk/shared-auth/token";
import { FlinkkInventoryAPI, CartMetadataUpdateRequest } from "@flinkk/inventory-api";
import { prisma } from "@flinkk/database/prisma";
import { withTenantRBAC } from "@flinkk/shared-rbac";

export const dynamic = "force-dynamic";

/**
 * PATCH /api/hotel-management/cart/[cartId]/metadata - Update cart metadata
 * 
 * This endpoint handles updating cart metadata in the inventory system.
 * It's used by the traveller info system to store traveller information.
 */
export const PATCH = withTenantRBAC(
  async (
    req: NextRequest,
    rbacPrisma: any,
    tenantId: string,
    { params }: { params: Promise<{ cartId: string }> }
  ) => {
    try {
      const { userId } = await getToken({ req });
      const { cartId } = await params;

      if (!userId || !tenantId) {
        return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
      }

      if (!cartId) {
        return NextResponse.json(
          { error: "Cart ID is required" },
          { status: 400 }
        );
      }

      // Parse request body
      const body: CartMetadataUpdateRequest = await req.json();

      if (!body.metadata) {
        return NextResponse.json(
          { error: "Metadata is required" },
          { status: 400 }
        );
      }

      // Get inventory configuration for the tenant
      const inventoryConfig = await prisma.inventoryConfiguration.findUnique({
        where: {
          tenantId,
        },
        select: {
          apiUrl: true,
          token: true,
          isActive: true,
          verificationStatus: true,
        },
      });

      // Check if inventory is configured and active
      if (
        !inventoryConfig ||
        !inventoryConfig.apiUrl ||
        !inventoryConfig.token
      ) {
        return NextResponse.json(
          { error: "Inventory system not configured for this tenant" },
          { status: 400 }
        );
      }

      if (!inventoryConfig.isActive) {
        return NextResponse.json(
          { error: "Inventory system is not active" },
          { status: 400 }
        );
      }

      // Create inventory API instance
      const inventoryAPI = new FlinkkInventoryAPI({
        apiUrl: inventoryConfig.apiUrl,
        token: inventoryConfig.token,
      });

      try {
        // Update cart metadata via inventory API
        const result = await inventoryAPI.updateCartMetadata(cartId, body);

        console.log("✅ Cart metadata updated successfully:", result);

        return NextResponse.json({
          success: true,
          message: "Cart metadata updated successfully",
          cart: result.cart,
        });
      } catch (inventoryError: any) {
        console.error("❌ Inventory API error:", inventoryError);
        
        // Extract error message from inventory API response
        let errorMessage = "Failed to update cart metadata";
        if (inventoryError.message) {
          errorMessage = inventoryError.message;
        } else if (typeof inventoryError === "string") {
          errorMessage = inventoryError;
        }

        return NextResponse.json(
          { 
            error: "Failed to update cart metadata",
            message: errorMessage,
            details: inventoryError
          },
          { status: 502 }
        );
      }
    } catch (error) {
      console.error("❌ Error updating cart metadata:", error);

      const errorMessage =
        error instanceof Error
          ? error.message
          : "Failed to update cart metadata";

      return NextResponse.json(
        { 
          error: "Internal server error",
          message: errorMessage
        },
        { status: 500 }
      );
    }
  }
);
