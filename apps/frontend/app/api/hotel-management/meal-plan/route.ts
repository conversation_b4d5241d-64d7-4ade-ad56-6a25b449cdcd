import { NextRequest, NextResponse } from "next/server";
import { FlinkkInventoryAPI } from "@flinkk/inventory-api";
import { prisma } from "@flinkk/database/prisma";
import { getToken } from "@flinkk/shared-auth/token";

/**
 * GET /api/hotel-management/meal-plan?hotel_id={hotelId}
 *
 * Fetch meal plans for a specific hotel from the inventory API
 */
export async function GET(request: NextRequest) {
  try {
    const { tenantId } = await getToken({ req: request });

    if (!tenantId) {
      return NextResponse.json(
        { error: "Authentication required" },
        { status: 401 }
      );
    }

    // Parse query parameters
    const { searchParams } = new URL(request.url);
    const hotel_id = searchParams.get("hotel_id");

    if (!hotel_id) {
      return NextResponse.json(
        { error: "Hotel ID is required" },
        { status: 400 }
      );
    }

    // Get inventory configuration for the tenant
    const inventoryConfig = await prisma.inventoryConfiguration.findUnique({
      where: {
        tenantId,
      },
      select: {
        apiUrl: true,
        token: true,
        isActive: true,
        verificationStatus: true,
      },
    });

    // Check if inventory is configured and active
    if (!inventoryConfig || !inventoryConfig.apiUrl || !inventoryConfig.token) {
      console.log("Inventory not configured");
      return NextResponse.json(
        { error: "Inventory service not configured" },
        { status: 503 }
      );
    }

    if (
      !inventoryConfig.isActive ||
      inventoryConfig.verificationStatus !== "connected"
    ) {
      console.log("Inventory service not available");
      return NextResponse.json(
        { error: "Inventory service not available" },
        { status: 503 }
      );
    }

    // Create inventory API instance
    const inventoryAPI = new FlinkkInventoryAPI({
      apiUrl: inventoryConfig.apiUrl,
      token: inventoryConfig.token,
    });

    try {
      // Fetch hotel pricing data which includes meal plans
      const pricingData = await inventoryAPI.getHotelPricing({
        hotel_id: hotel_id,
      });

      if (!pricingData.success) {
        return NextResponse.json(
          { error: "Failed to fetch hotel pricing data" },
          { status: 502 }
        );
      }

      // Extract meal plans from pricing data
      const mealPlans = pricingData.pricing?.meal_plans || [];

      return NextResponse.json({
        meal_plans: mealPlans,
      });
    } catch (inventoryError: any) {
      console.error("Inventory API error:", inventoryError);
      return NextResponse.json(
        { error: "Failed to fetch meal plans from inventory service" },
        { status: 502 }
      );
    }
  } catch (error) {
    console.error("Error fetching meal plans:", error);
    return NextResponse.json(
      { error: "Internal server error" },
      { status: 500 }
    );
  }
}
